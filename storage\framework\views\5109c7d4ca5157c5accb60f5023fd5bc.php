<div class="reaction-wrapper relative inline-block">
    <!-- Main Reaction Button -->
    <button class="reaction-btn flex items-center space-x-2 <?php echo e($userReaction ? \App\Models\Reaction::getReactionDetails($userReaction->type)['color'] : 'text-gray-500'); ?> transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
            wire:click="toggleReaction('<?php echo e($userReaction ? $userReaction->type : 'like'); ?>')"
            @mouseenter="showReactionPopup = true"
            @mouseleave="showReactionPopup = false"
            x-data="{ showReactionPopup: false }"
            x-init="
                $el.addEventListener('touchstart', function(e) {
                    if (e.touches.length === 1) {
                        touchStartTime = Date.now();
                        longPressTimer = setTimeout(() => {
                            showReactionPopup = true;
                            navigator.vibrate && navigator.vibrate(50);
                        }, 500);
                    }
                });
                $el.addEventListener('touchend', function(e) {
                    clearTimeout(longPressTimer);
                    if (Date.now() - touchStartTime < 500) {
                        showReactionPopup = false;
                    }
                });
            ">

        <!--[if BLOCK]><![endif]--><?php if($userReaction): ?>
            <?php
                $reactionDetails = \App\Models\Reaction::getReactionDetails($userReaction->type);
            ?>
            <!-- User has reacted - show their reaction -->
            <img src="<?php echo e($reactionDetails['emoji']); ?>" alt="<?php echo e($reactionDetails['label']); ?>" class="w-5 h-5 reaction-emoji"
                 onerror="this.style.display='none';">
            <span class="text-sm font-medium"><?php echo e($reactionDetails['label']); ?></span>
        <?php else: ?>
            <!-- Default like button -->
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-2-2v9a2 2 0 002 2h2m0-10V9a2 2 0 002 2h2" />
            </svg>
            <span class="text-sm font-medium">Like</span>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!--[if BLOCK]><![endif]--><?php if($totalReactions > 0): ?>
            <span class="text-sm text-gray-600">(<?php echo e($totalReactions); ?>)</span>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        <!-- Reaction Popup -->
        <div x-show="showReactionPopup"
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 scale-95 transform translate-y-2"
             x-transition:enter-end="opacity-100 scale-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 scale-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 scale-95 transform translate-y-2"
             class="reaction-popup absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-white rounded-full shadow-lg border border-gray-200 px-3 py-2 flex space-x-2 z-50"
             @mouseenter="showReactionPopup = true"
             @mouseleave="showReactionPopup = false">
            
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = ['like', 'love', 'haha', 'wow', 'sad', 'angry']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reactionType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $details = \App\Models\Reaction::getReactionDetails($reactionType);
                ?>
                <button wire:click="toggleReaction('<?php echo e($reactionType); ?>')"
                        class="reaction-option hover:scale-125 transform transition-transform duration-150 p-1 rounded-full hover:bg-gray-100"
                        title="<?php echo e($details['label']); ?>">
                    <img src="<?php echo e($details['emoji']); ?>" alt="<?php echo e($details['label']); ?>" class="w-8 h-8">
                </button>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </button>
</div>

<style>
/* Reaction popup shadow and border */
.reaction-popup {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* Reaction option hover effects */
.reaction-option:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Animation for reaction changes */
.reaction-emoji {
    animation: reactionPop 0.3s ease-out;
}

@keyframes reactionPop {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Mobile touch improvements */
@media (max-width: 768px) {
    .reaction-popup {
        bottom: 120%;
        padding: 8px 12px;
    }
    
    .reaction-option {
        padding: 8px;
    }
    
    .reaction-option img {
        width: 32px;
        height: 32px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .reaction-popup {
        background-color: #374151;
        border-color: #4B5563;
    }
    
    .reaction-option:hover {
        background-color: #4B5563;
    }
}
</style>

<!-- Initialize reactions for this component -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Listen for reaction updates
    Livewire.on('reactionUpdated', (data) => {
        // Update summary bars if they exist
        if (window.postSummaryUpdater) {
            window.postSummaryUpdater.updateShareReactionSummary(data.shareId, {
                total: data.totalReactions,
                counts: data.reactionCounts
            });
        }
    });
});
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/shared-post-reactions.blade.php ENDPATH**/ ?>