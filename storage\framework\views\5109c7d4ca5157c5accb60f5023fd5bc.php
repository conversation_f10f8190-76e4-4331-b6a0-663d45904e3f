<div>
    <!-- Main Reaction Button -->
    <div class="relative inline-block">
        <div class="relative">
            <?php
                $userReactionDetails = $userReaction ? $this->availableReactions[$userReaction->type] ?? null : null;
                $buttonColor = $userReactionDetails ? $userReactionDetails['color'] : 'text-gray-600 hover:text-blue-600';
                $buttonText = $userReactionDetails ? $userReactionDetails['label'] : 'Like';
                $buttonIcon = $userReactionDetails ? $userReactionDetails['emoji'] : '👍';
            ?>

            <button wire:click="toggleReaction('like')"
                    wire:mouseenter="showPopup"
                    wire:mouseleave="hidePopup"
                    class="flex items-center space-x-1 <?php echo e($buttonColor); ?> transition-all duration-200 hover:bg-gray-100 px-3 py-2 rounded-lg text-sm font-medium group">
                <span class="text-lg group-hover:scale-110 transition-transform duration-200"><?php echo e($buttonIcon); ?></span>
                <span><?php echo e($buttonText); ?></span>
            </button>

            <!-- Reaction Popup -->
            <!--[if BLOCK]><![endif]--><?php if($showReactionPopup): ?>
                <div class="absolute bottom-full left-0 mb-2 z-50 animate-in fade-in slide-in-from-bottom-2 duration-200"
                     wire:mouseenter="showPopup"
                     wire:mouseleave="hidePopup">
                    <div class="bg-white rounded-full shadow-lg border border-gray-200 px-2 py-2 flex space-x-1">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->availableReactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $details): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <button wire:click="toggleReaction('<?php echo e($type); ?>')"
                                    class="relative group hover:scale-125 transition-all duration-200 p-2 rounded-full hover:bg-gray-100">
                                <span class="text-2xl"><?php echo e($details['emoji']); ?></span>

                                <!-- Tooltip -->
                                <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                                    <?php echo e($details['label']); ?>

                                    <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                                </div>
                            </button>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <!-- Reaction Count Display (if any reactions exist) -->
        <!--[if BLOCK]><![endif]--><?php if($totalReactions > 0): ?>
            <div class="absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                <?php echo e($totalReactions > 99 ? '99+' : $totalReactions); ?>

            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>


</div>

<style>
@keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slide-in-from-bottom-2 {
    from { transform: translateY(0.5rem); }
    to { transform: translateY(0); }
}

.animate-in {
    animation-fill-mode: both;
}

.fade-in {
    animation-name: fade-in;
}

.slide-in-from-bottom-2 {
    animation-name: slide-in-from-bottom-2;
}

.duration-200 {
    animation-duration: 200ms;
}
</style>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/shared-post-reactions.blade.php ENDPATH**/ ?>