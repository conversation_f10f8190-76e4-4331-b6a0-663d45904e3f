/**
 * Shared Post Summary Updater
 * Handles real-time updates for shared post reaction and comment summaries
 */

class SharedPostSummaryUpdater {
    constructor() {
        this.init();
    }

    init() {
        // Listen for Livewire events
        document.addEventListener('DOMContentLoaded', () => {
            this.bindEvents();
        });
    }

    bindEvents() {
        // Listen for reaction updates
        if (window.Livewire) {
            Livewire.on('reactionUpdated', (data) => {
                if (data.shareId) {
                    this.updateShareReactionSummary(data.shareId, data);
                }
            });

            // Listen for comment updates
            Livewire.on('commentAdded', (data) => {
                if (data.shareId) {
                    this.updateShareCommentSummary(data.shareId);
                }
            });

            Livewire.on('commentDeleted', (data) => {
                if (data.shareId) {
                    this.updateShareCommentSummary(data.shareId);
                }
            });

            Livewire.on('updateCommentCount', (data) => {
                if (data.shareId) {
                    this.updateShareCommentCount(data.shareId, data.count);
                }
            });
        }
    }

    /**
     * Update share reaction summary
     */
    updateShareReactionSummary(shareId, data) {
        const summaryBar = document.getElementById(`share-summary-bar-${shareId}`);
        const reactionSummary = document.getElementById(`share-reaction-summary-${shareId}`);
        const reactionCount = document.getElementById(`share-reaction-count-${shareId}`);
        const reactionEmojis = document.getElementById(`share-reaction-emojis-${shareId}`);

        if (!summaryBar) return;

        if (data.totalReactions > 0) {
            summaryBar.style.display = 'block';
            
            if (reactionSummary) {
                reactionSummary.style.display = 'flex';
            }
            
            if (reactionCount) {
                reactionCount.textContent = data.totalReactions;
            }

            // Update reaction emojis
            if (reactionEmojis && data.reactionCounts) {
                this.updateReactionEmojis(shareId, data.reactionCounts);
            }
        } else {
            if (reactionSummary) {
                reactionSummary.style.display = 'none';
            }
            
            // Hide summary bar if no reactions and no comments
            this.checkSummaryBarVisibility(shareId);
        }
    }

    /**
     * Update reaction emojis display
     */
    updateReactionEmojis(shareId, reactionCounts) {
        const reactionEmojis = document.getElementById(`share-reaction-emojis-${shareId}`);
        if (!reactionEmojis) return;

        // Sort reactions by count and take top 3
        const sortedReactions = Object.entries(reactionCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3);

        // Clear existing emojis
        reactionEmojis.innerHTML = '';

        // Add new emojis
        sortedReactions.forEach(([reactionType, count]) => {
            const reactionDetails = this.getReactionDetails(reactionType);
            if (reactionDetails) {
                const emojiContainer = document.createElement('div');
                emojiContainer.className = 'w-5 h-5 bg-white rounded-full border border-gray-200 flex items-center justify-center';
                
                const emojiImg = document.createElement('img');
                emojiImg.src = reactionDetails.emoji;
                emojiImg.alt = reactionDetails.label;
                emojiImg.className = 'w-3 h-3';
                
                emojiContainer.appendChild(emojiImg);
                reactionEmojis.appendChild(emojiContainer);
            }
        });
    }

    /**
     * Update share comment summary
     */
    updateShareCommentSummary(shareId) {
        // Fetch updated comment count from server
        this.fetchShareCommentCount(shareId).then(count => {
            this.updateShareCommentCount(shareId, count);
        });
    }

    /**
     * Update share comment count
     */
    updateShareCommentCount(shareId, count) {
        const summaryBar = document.getElementById(`share-summary-bar-${shareId}`);
        const commentSummary = document.getElementById(`share-comment-summary-${shareId}`);
        const commentButton = document.getElementById(`share-comments-count-${shareId}`);

        if (commentSummary) {
            if (count > 0) {
                commentSummary.style.display = 'inline';
                commentSummary.textContent = `${count} comment${count !== 1 ? 's' : ''}`;
                
                if (summaryBar) {
                    summaryBar.style.display = 'block';
                }
            } else {
                commentSummary.style.display = 'none';
                this.checkSummaryBarVisibility(shareId);
            }
        }

        if (commentButton) {
            commentButton.textContent = `${count} comment${count !== 1 ? 's' : ''}`;
        }
    }

    /**
     * Check if summary bar should be visible
     */
    checkSummaryBarVisibility(shareId) {
        const summaryBar = document.getElementById(`share-summary-bar-${shareId}`);
        const reactionSummary = document.getElementById(`share-reaction-summary-${shareId}`);
        const commentSummary = document.getElementById(`share-comment-summary-${shareId}`);

        if (!summaryBar) return;

        const hasReactions = reactionSummary && reactionSummary.style.display !== 'none';
        const hasComments = commentSummary && commentSummary.style.display !== 'none';

        if (hasReactions || hasComments) {
            summaryBar.style.display = 'block';
        } else {
            summaryBar.style.display = 'none';
        }
    }

    /**
     * Fetch share comment count from server
     */
    async fetchShareCommentCount(shareId) {
        try {
            const response = await fetch(`/api/shares/${shareId}/comments/count`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                return data.count || 0;
            }
        } catch (error) {
            console.error('Error fetching share comment count:', error);
        }
        return 0;
    }

    /**
     * Get reaction details (emoji, label, color)
     */
    getReactionDetails(reactionType) {
        const reactions = {
            'like': {
                emoji: '/images/reactions/like.png',
                label: 'Like',
                color: 'text-blue-600'
            },
            'love': {
                emoji: '/images/reactions/love.png',
                label: 'Love',
                color: 'text-red-600'
            },
            'haha': {
                emoji: '/images/reactions/haha.png',
                label: 'Haha',
                color: 'text-yellow-600'
            },
            'wow': {
                emoji: '/images/reactions/wow.png',
                label: 'Wow',
                color: 'text-yellow-600'
            },
            'sad': {
                emoji: '/images/reactions/sad.png',
                label: 'Sad',
                color: 'text-yellow-600'
            },
            'angry': {
                emoji: '/images/reactions/angry.png',
                label: 'Angry',
                color: 'text-red-600'
            }
        };

        return reactions[reactionType] || null;
    }

    /**
     * Trigger update after a reaction change
     */
    onReactionChange(shareId) {
        // Debounced update
        setTimeout(() => {
            this.updateShareReactionSummary(shareId, {});
        }, 100);
    }

    /**
     * Trigger update after a comment change
     */
    onCommentChange(shareId) {
        // Debounced update
        setTimeout(() => {
            this.updateShareCommentSummary(shareId);
        }, 100);
    }
}

// Initialize the shared post summary updater
window.sharedPostSummaryUpdater = new SharedPostSummaryUpdater();

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SharedPostSummaryUpdater;
}
