<div class="comment-item py-4 px-4 hover:bg-gray-50 transition-colors duration-150">
    <div class="flex space-x-3">
        <a href="{{ route('profile.user', $comment->user) }}" class="flex-shrink-0">
            <img class="h-10 w-10 rounded-full ring-1 ring-gray-300 shadow-sm"
                 src="{{ $comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                 alt="{{ $comment->user->name }}">
        </a>
        <div class="flex-1 min-w-0">
            <div class="bg-gray-100 rounded-xl p-4 shadow-sm border border-gray-200">
                <div class="flex items-center space-x-2 mb-2">
                    <a href="{{ route('profile.user', $comment->user) }}" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                        {{ $comment->user->name }}
                    </a>
                    <span class="text-xs text-gray-500">{{ $comment->created_at->diffForHumans() }}</span>
                    @if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin()))
                        <div class="relative ml-auto" x-data="{ open: false }">
                            <button @click="open = !open" class="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-200">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition
                                 class="absolute right-0 top-8 w-32 bg-white shadow-lg rounded-md border border-gray-200 py-1 z-20">
                                <button wire:click="startEdit" @click="open = false"
                                        class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    Edit
                                </button>
                                <button wire:click="deleteComment" @click="open = false"
                                        class="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50">
                                    Delete
                                </button>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Comment Content -->
                @if($isEditing)
                    <div class="mt-2">
                        <textarea wire:model="editContent" rows="2"
                                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm"></textarea>
                        <div class="mt-2 flex justify-end space-x-2">
                            <button wire:click="cancelEdit" class="px-3 py-1 text-xs text-gray-600 hover:text-gray-800">
                                Cancel
                            </button>
                            <button wire:click="saveEdit" class="px-3 py-1 bg-custom-green text-white text-xs font-medium rounded hover:bg-custom-second-darkest">
                                Save
                            </button>
                        </div>
                    </div>
                @else
                    <div class="text-gray-900 text-sm">
                        {!! nl2br(e($comment->content)) !!}
                    </div>
                @endif
            </div>

            <!-- Comment Actions -->
            <div class="flex items-center space-x-4 mt-2 ml-4">
                <!-- Like Button -->
                @php
                    $userReaction = auth()->check() ? $comment->reactions->where('user_id', auth()->id())->first() : null;
                    $likesCount = $comment->reactions->where('type', 'like')->count();
                @endphp
                <button wire:click="toggleReaction('like')" 
                        class="flex items-center space-x-1 text-xs {{ $userReaction && $userReaction->type === 'like' ? 'text-red-600 font-medium' : 'text-gray-500' }} hover:text-red-600 transition-colors">
                    <svg class="w-4 h-4" fill="{{ $userReaction && $userReaction->type === 'like' ? 'currentColor' : 'none' }}" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    <span>Like</span>
                    @if($likesCount > 0)
                        <span>({{ $likesCount }})</span>
                    @endif
                </button>

                <!-- Reply Button -->
                <button wire:click="showReplyForm" class="text-xs text-gray-500 hover:text-gray-700 transition-colors">
                    Reply
                </button>

                <!-- View Replies Button -->
                @if($comment->replies && $comment->replies->count() > 0)
                    <button wire:click="toggleReplies" class="flex items-center space-x-1 text-xs text-gray-600 hover:text-gray-800 font-medium transition-colors">
                        <svg class="w-4 h-4 transform transition-transform {{ $showReplies ? 'rotate-180' : '' }}" viewBox="0 0 24 24">
                            <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                        </svg>
                        <span>
                            {{ $showReplies ? 'Hide' : 'View' }} {{ $comment->replies->count() }} {{ $comment->replies->count() === 1 ? 'reply' : 'replies' }}
                        </span>
                    </button>
                @endif
            </div>

            <!-- Reply Form -->
            @if($showReplyForm)
                <div class="mt-3 ml-4">
                    <form wire:submit.prevent="addReply">
                        <div class="flex space-x-3">
                            <div class="flex-shrink-0">
                                <img class="h-8 w-8 rounded-full ring-1 ring-gray-300 shadow-sm"
                                     src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                                     alt="{{ auth()->user()->name }}">
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="relative">
                                    <textarea wire:model="replyContent" rows="1"
                                              placeholder="Write a reply..."
                                              class="w-full bg-gray-50 text-gray-900 border-2 border-gray-300 rounded-full px-4 py-2 pr-20 focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-transparent resize-none text-sm"
                                              style="min-height: 36px;" 
                                              required></textarea>
                                    <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                                        <button type="button" wire:click="hideReplyForm"
                                                class="p-1 text-gray-400 hover:text-gray-600 rounded-full">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                        <button type="submit"
                                                class="p-1 bg-custom-green text-white rounded-full hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green shadow-sm transition-all duration-200">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            @endif

            <!-- Replies Section -->
            @if($showReplies && $comment->replies && $comment->replies->count() > 0)
                <div class="mt-3 ml-4 space-y-3">
                    @foreach($comment->replies as $reply)
                        <div class="flex space-x-3">
                            <a href="{{ route('profile.user', $reply->user) }}" class="flex-shrink-0">
                                <img class="h-8 w-8 rounded-full ring-1 ring-gray-300 shadow-sm"
                                     src="{{ $reply->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($reply->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($reply->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                     alt="{{ $reply->user->name }}">
                            </a>
                            <div class="flex-1 min-w-0">
                                <div class="bg-gray-50 rounded-xl p-3 shadow-sm border border-gray-100">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <a href="{{ route('profile.user', $reply->user) }}" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                                            {{ $reply->user->name }}
                                        </a>
                                        <span class="text-xs text-gray-500">{{ $reply->created_at->diffForHumans() }}</span>
                                    </div>
                                    <div class="text-gray-900 text-sm">
                                        {!! nl2br(e($reply->content)) !!}
                                    </div>
                                </div>
                                
                                <!-- Reply Actions -->
                                <div class="flex items-center space-x-4 mt-1 ml-3">
                                    @php
                                        $replyUserReaction = auth()->check() ? $reply->reactions->where('user_id', auth()->id())->first() : null;
                                        $replyLikesCount = $reply->reactions->where('type', 'like')->count();
                                    @endphp
                                    <button class="flex items-center space-x-1 text-xs {{ $replyUserReaction && $replyUserReaction->type === 'like' ? 'text-red-600 font-medium' : 'text-gray-500' }} hover:text-red-600 transition-colors">
                                        <svg class="w-3 h-3" fill="{{ $replyUserReaction && $replyUserReaction->type === 'like' ? 'currentColor' : 'none' }}" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                        </svg>
                                        <span>Like</span>
                                        @if($replyLikesCount > 0)
                                            <span>({{ $replyLikesCount }})</span>
                                        @endif
                                    </button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>
    </div>
</div>
