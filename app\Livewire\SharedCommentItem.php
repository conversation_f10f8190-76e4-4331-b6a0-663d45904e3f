<?php

namespace App\Livewire;

use App\Models\Comment;
use App\Models\Share;
use App\Models\Reaction;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class SharedCommentItem extends Component
{
    public Comment $comment;
    public Share $share;
    public $showReplies = false;
    public $showReplyForm = false;
    public $replyContent = '';
    public $isEditing = false;
    public $editContent = '';

    public function mount(Comment $comment, Share $share)
    {
        $this->comment = $comment;
        $this->share = $share;
        $this->editContent = $comment->content;
    }

    public function toggleReplies()
    {
        $this->showReplies = !$this->showReplies;
        if ($this->showReplies) {
            $this->comment->load(['replies.user', 'replies.reactions']);
        }
    }

    public function showReplyForm()
    {
        $this->showReplyForm = true;
    }

    public function hideReplyForm()
    {
        $this->showReplyForm = false;
        $this->replyContent = '';
    }

    public function addReply()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->validate([
            'replyContent' => 'required|string|max:1000'
        ]);

        $reply = Comment::create([
            'content' => $this->replyContent,
            'user_id' => Auth::id(),
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'parent_id' => $this->comment->id
        ]);

        $this->replyContent = '';
        $this->showReplyForm = false;
        $this->showReplies = true;
        
        // Refresh the comment with replies
        $this->comment->load(['replies.user', 'replies.reactions']);

        // Emit event to parent component
        $this->dispatch('replyAdded', [
            'shareId' => $this->share->id,
            'parentId' => $this->comment->id,
            'reply' => $reply->load('user', 'reactions')
        ]);
    }

    public function startEdit()
    {
        if (Auth::check() && (Auth::id() === $this->comment->user_id || Auth::user()->isAdmin())) {
            $this->isEditing = true;
            $this->editContent = $this->comment->content;
        }
    }

    public function cancelEdit()
    {
        $this->isEditing = false;
        $this->editContent = $this->comment->content;
    }

    public function saveEdit()
    {
        if (!Auth::check() || (Auth::id() !== $this->comment->user_id && !Auth::user()->isAdmin())) {
            return;
        }

        $this->validate([
            'editContent' => 'required|string|max:1000'
        ]);

        $this->comment->update(['content' => $this->editContent]);
        $this->isEditing = false;

        $this->dispatch('commentUpdated', [
            'shareId' => $this->share->id,
            'commentId' => $this->comment->id
        ]);
    }

    public function deleteComment()
    {
        if (Auth::check() && (Auth::id() === $this->comment->user_id || Auth::user()->isAdmin())) {
            $this->comment->delete();

            $this->dispatch('commentDeleted', [
                'shareId' => $this->share->id,
                'commentId' => $this->comment->id
            ]);
        }
    }

    public function toggleReaction($reactionType = 'like')
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $existingReaction = $this->comment->reactions()
            ->where('user_id', $user->id)
            ->first();

        if ($existingReaction) {
            if ($existingReaction->type === $reactionType) {
                // Same reaction - remove it (toggle off)
                $existingReaction->delete();
            } else {
                // Different reaction - update it
                $existingReaction->update(['type' => $reactionType]);
            }
        } else {
            // Create new reaction
            Reaction::create([
                'reactable_type' => Comment::class,
                'reactable_id' => $this->comment->id,
                'user_id' => $user->id,
                'type' => $reactionType,
            ]);
        }

        // Refresh the comment with reactions
        $this->comment->load('reactions');
    }

    public function render()
    {
        return view('livewire.shared-comment-item');
    }
}
