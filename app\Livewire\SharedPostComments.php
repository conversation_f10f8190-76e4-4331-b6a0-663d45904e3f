<?php

namespace App\Livewire;

use App\Models\Share;
use App\Models\Comment;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class SharedPostComments extends Component
{
    public Share $share;
    public $comments = [];
    public $newComment = '';
    public $showComments = false;
    public $commentsLimit = 3;

    protected $listeners = [
        'commentAdded' => 'refreshComments',
        'commentUpdated' => 'refreshComments',
        'commentDeleted' => 'refreshComments',
        'toggleComments' => 'toggleCommentsVisibility'
    ];

    public function mount(Share $share)
    {
        $this->share = $share;
        $this->loadComments();
    }

    public function loadComments()
    {
        $this->comments = $this->share->comments()
            ->with(['user', 'reactions', 'replies.user', 'replies.reactions'])
            ->whereNull('parent_id')
            ->latest()
            ->limit($this->commentsLimit)
            ->get();
    }

    public function toggleCommentsVisibility($data = null)
    {
        if ($data && isset($data['shareId']) && $data['shareId'] == $this->share->id) {
            $this->showComments = !$this->showComments;
            if ($this->showComments) {
                $this->loadComments();
            }
        }
    }

    public function addComment()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->validate([
            'newComment' => 'required|string|max:1000'
        ]);

        $comment = Comment::create([
            'content' => $this->newComment,
            'user_id' => Auth::id(),
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'parent_id' => null
        ]);

        $this->newComment = '';
        $this->loadComments();

        // Emit event for real-time updates
        $this->dispatch('commentAdded', [
            'shareId' => $this->share->id,
            'comment' => $comment->load('user', 'reactions')
        ]);

        // Update comment count in summary
        $this->dispatch('updateCommentCount', [
            'shareId' => $this->share->id,
            'count' => $this->share->comments()->count()
        ]);
    }

    public function loadMoreComments()
    {
        $this->commentsLimit += 5;
        $this->loadComments();
    }

    public function refreshComments()
    {
        $this->loadComments();
    }

    public function render()
    {
        return view('livewire.shared-post-comments');
    }
}
