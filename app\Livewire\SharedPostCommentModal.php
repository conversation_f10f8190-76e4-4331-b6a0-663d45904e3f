<?php

namespace App\Livewire;

use App\Models\Share;
use App\Models\Comment;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class SharedPostCommentModal extends Component
{
    public Share $share;
    public $isOpen = false;
    public $comments = [];
    public $sortBy = 'newest';
    public $newComment = '';

    protected $listeners = [
        'openCommentModal' => 'openModal',
        'closeCommentModal' => 'closeModal',
        'commentAdded' => 'refreshComments',
        'commentUpdated' => 'refreshComments',
        'commentDeleted' => 'refreshComments'
    ];

    public function mount(Share $share)
    {
        $this->share = $share;
        $this->loadComments();
    }

    public function openModal($data = null)
    {
        if ($data && isset($data['shareId']) && $data['shareId'] == $this->share->id) {
            $this->isOpen = true;
            $this->loadComments();
        }
    }

    public function closeModal()
    {
        $this->isOpen = false;
        $this->newComment = '';
    }

    public function loadComments()
    {
        $query = $this->share->comments()
            ->with(['user', 'reactions', 'replies.user', 'replies.reactions'])
            ->whereNull('parent_id');

        switch ($this->sortBy) {
            case 'oldest':
                $query->oldest();
                break;
            case 'most_liked':
                $query->withCount('reactions')->orderBy('reactions_count', 'desc');
                break;
            default: // newest
                $query->latest();
                break;
        }

        $this->comments = $query->get();
    }

    public function changeSortOrder($sortBy)
    {
        $this->sortBy = $sortBy;
        $this->loadComments();
    }

    public function addComment()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $this->validate([
            'newComment' => 'required|string|max:1000'
        ]);

        $comment = Comment::create([
            'content' => $this->newComment,
            'user_id' => Auth::id(),
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'parent_id' => null
        ]);

        $this->newComment = '';
        $this->loadComments();

        // Emit event for real-time updates
        $this->dispatch('commentAdded', [
            'shareId' => $this->share->id,
            'comment' => $comment->load('user', 'reactions')
        ]);
    }

    public function addReply($parentId, $content)
    {
        if (!Auth::check()) {
            return;
        }

        $reply = Comment::create([
            'content' => $content,
            'user_id' => Auth::id(),
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'parent_id' => $parentId
        ]);

        $this->loadComments();

        // Emit event for real-time updates
        $this->dispatch('replyAdded', [
            'shareId' => $this->share->id,
            'parentId' => $parentId,
            'reply' => $reply->load('user', 'reactions')
        ]);
    }

    public function deleteComment($commentId)
    {
        if (!Auth::check()) {
            return;
        }

        $comment = Comment::find($commentId);
        
        if ($comment && ($comment->user_id === Auth::id() || Auth::user()->isAdmin())) {
            $comment->delete();
            $this->loadComments();

            $this->dispatch('commentDeleted', [
                'shareId' => $this->share->id,
                'commentId' => $commentId
            ]);
        }
    }

    public function refreshComments()
    {
        $this->loadComments();
    }

    public function render()
    {
        return view('livewire.shared-post-comment-modal');
    }
}
