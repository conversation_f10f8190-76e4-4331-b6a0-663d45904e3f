<?php

namespace App\Livewire;

use App\Models\Share;
use App\Models\Reaction;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class SharedPostReactions extends Component
{
    public Share $share;
    public $userReaction = null;
    public $reactionCounts = [];
    public $totalReactions = 0;

    public function mount(Share $share)
    {
        $this->share = $share;
        $this->loadReactionData();
    }

    public function loadReactionData()
    {
        // Load user's current reaction
        if (Auth::check()) {
            $this->userReaction = $this->share->reactions()
                ->where('user_id', Auth::id())
                ->first();
        }

        // Load reaction counts
        $this->reactionCounts = $this->share->reactions()
            ->selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        $this->totalReactions = array_sum($this->reactionCounts);
    }

    public function toggleReaction($reactionType = null)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();
        $existingReaction = $this->share->reactions()
            ->where('user_id', $user->id)
            ->first();

        if ($reactionType === null) {
            // Remove reaction if it exists
            if ($existingReaction) {
                $existingReaction->delete();
            }
        } else {
            // Create or update reaction
            if ($existingReaction) {
                if ($existingReaction->type === $reactionType) {
                    // Same reaction - remove it (toggle off)
                    $existingReaction->delete();
                } else {
                    // Different reaction - update it
                    $existingReaction->update(['type' => $reactionType]);
                }
            } else {
                // Create new reaction
                Reaction::create([
                    'reactable_type' => Share::class,
                    'reactable_id' => $this->share->id,
                    'user_id' => $user->id,
                    'type' => $reactionType,
                ]);
            }
        }

        // Reload reaction data
        $this->loadReactionData();

        // Emit event for real-time updates
        $this->dispatch('reactionUpdated', [
            'shareId' => $this->share->id,
            'totalReactions' => $this->totalReactions,
            'reactionCounts' => $this->reactionCounts,
            'userReaction' => $this->userReaction?->type
        ]);

        // Also emit to parent components for summary updates
        $this->dispatch('updateReactionSummary', [
            'shareId' => $this->share->id,
            'totalReactions' => $this->totalReactions,
            'reactionCounts' => $this->reactionCounts
        ]);
    }

    public function render()
    {
        return view('livewire.shared-post-reactions');
    }
}
